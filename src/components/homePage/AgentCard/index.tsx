import React from 'react';
import { Tooltip } from 'antd';

interface AgentCardProps {
  icon: string;
  title: string;
  desc: string;
  tag?: string;
  creator: string;
  onClick?: () => void;
}

export default function AgentCard({
  icon,
  title,
  desc,
  tag,
  creator,
  onClick
}: AgentCardProps) {
  return (
    <div
      className="bg-white hover:bg-[#f7f7f7] box-border content-stretch flex flex-col gap-4 items-start justify-start px-4 py-[19px] relative rounded-xl size-full cursor-pointer transition-all duration-300 hover:shadow-[0px_10px_30px_-3px_rgba(75,85,105,0.1),0px_15px_45px_7px_rgba(27,37,50,0.06)] min-w-[304px]"
      onClick={onClick}
    >
      {/* 边框 */}
      <div
        aria-hidden="true"
        className="absolute border border-[#d6d6d6] border-solid inset-0 pointer-events-none rounded-xl"
      />

      {/* 头部区域 */}
      <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0 w-full">
        {/* 头像 */}
        <div
          className="bg-center bg-cover bg-no-repeat rounded-full shrink-0 size-20"
          style={{ backgroundImage: `url('${icon}')` }}
        />

        {/* 右侧内容 */}
        <div className="basis-0 box-border content-stretch flex flex-col gap-0.5 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
          {/* 标题和标签行 */}
          <div className="box-border content-stretch flex flex-row items-center justify-between p-0 relative shrink-0 w-full">
            <div className="box-border content-stretch flex flex-row gap-2 items-center justify-start p-0 relative flex-1 min-w-0">
              <div className="font-['PingFang_SC',_sans-serif] font-semibold leading-[0] not-italic relative text-black text-base text-left flex-1 min-w-0">
                <Tooltip title={title} placement="top">
                  <p className="block leading-6 whitespace-nowrap overflow-hidden text-ellipsis">{title}</p>
                </Tooltip>
              </div>
              {tag && (
                <div className="bg-[#f7ecdb] box-border content-stretch flex flex-row gap-2.5 h-[18px] items-center justify-center px-2.5 py-2.5 relative rounded shrink-0 w-auto">
                  <div className="font-['PingFang_SC',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[#825a36] text-xs text-left text-nowrap">
                    <p className="block leading-5 whitespace-pre">{tag}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 描述 */}
          <div
            className="font-['PingFang_SC',_sans-serif] leading-[0] min-w-full not-italic overflow-hidden relative shrink-0 text-black text-xs text-left"
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            <p className="block leading-5">{desc}</p>
          </div>

          {/* 创建者 */}
          <div className="font-['PingFang_SC',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#7f7f7f] text-xs text-left text-nowrap">
            <p className="block leading-5 whitespace-pre">创建者：{creator}</p>
          </div>
        </div>
      </div>
    </div>
  );
}